<div class="pl-2 pr-2 pt-2">
  <div class="flex items-center">
    <mat-form-field appearance="outline">
      <mat-label for="label">HRN</mat-label>
      <input #inputRef matInput style="color: black;" aria-describedby="emailHelp" disabled
        [(ngModel)]="patientDetails.HRN" id="hrn" required hrn maxlength="100" autocomplete="off">
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label for="label">IDTYPE</mat-label>
      <mat-select [(ngModel)]="patientDetails.IDType" required>
        <mat-option *ngFor="let data of  visitData?.ID" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
      </mat-select>
      <mat-error class="text-xs" *ngIf="!patientDetails.IDType">
        IDType is required.
      </mat-error>

    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label for="label"><strong>ID</strong></mat-label>
      <input matInput type="text" [(ngModel)]="patientDetails.IDNo" id="idno" name="idno" required idno
        (keypress)="handleFindPatient($event)" aria-describedby="emailHelp" maxlength="100" autocomplete="off"
        autofocus>
      @if(page === "manualVisit"){
      <mat-icon matIconSuffix matTooltip="Search Individual" class="cursor-pointer" svgIcon="person_search"
        (click)="handleIndividualDialogOpen()" />
      }
      <mat-error class="text-xs" *ngIf="!patientDetails.IDNo || patientDetails.IDNo.length > 100">
        IDNo is required.
      </mat-error>
    </mat-form-field>
  </div>

  <div class="flex ">

    <mat-form-field appearance="outline" class="w-40">
      <mat-label>Title</mat-label>
      <mat-select [(ngModel)]="patientDetails.TitleCode" required>
        <mat-option *ngFor="let data of visitData?.TC" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
      </mat-select>
      <mat-error class="text-xs" *ngIf="!patientDetails.TitleCode" class="text-xs">
        Title is required.
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-1/2">
      <mat-label for="label"><strong>Name</strong></mat-label>
      <input matInput type="text" [(ngModel)]="patientDetails.Name" id="name" required name aria-describedby="emailHelp"
        maxlength="100" autocomplete="off">
      <mat-error class="text-xs" *ngIf="!patientDetails.Name || patientDetails.Name.length > 100">
        Name is required.
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label for="label">Gender</mat-label>
      <mat-select [(ngModel)]="patientDetails.Gender" id="gender" required gender>
        <mat-option *ngFor="let data of visitData?.SX" [value]="data.IDENTIFIER">
          {{data.DESCRIPTION}}
        </mat-option>

      </mat-select>
      <mat-error class="text-xs" *ngIf="!patientDetails.Gender">
        Gender is required.
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Date of Birth</mat-label>
      <input matInput [matDatepicker]="picker" [(ngModel)]="patientDetails.DateOfBirth" ec-AutoFormatDate required
        (dateChange)="onDateChanged($event)" placeholder="MM/dd/yyyy">
      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
      <mat-error class="text-xs" *ngIf="!patientDetails.DateOfBirth">Date of Birth is required</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-40">
      <mat-label>Age</mat-label>
      <input matInput type="text" [value]="calculateAge()" readonly placeholder="Age">
    </mat-form-field>
  </div>

</div>

<mat-tab-group>
  <mat-tab>
    <ng-template mat-tab-label>
      <mat-icon svgIcon="assignment_ind" class="text-blue-500"></mat-icon>
      <strong class="text-blue-500"> Personal Profile</strong>
    </ng-template>

    <div class="flex space-x-2 w-full pt-3 pl-2 pr-2">

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Marital Status</mat-label>
        <mat-select [(ngModel)]="patientDetails.MaritalStatus">
          <mat-option *ngFor="let data of visitData?.MS " [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Education</mat-label>
        <mat-select [(ngModel)]="patientDetails.Education">
          <mat-option *ngFor="let data of  visitData?.ED" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Occupation</mat-label>
        <mat-select [(ngModel)]="patientDetails.Occupation">
          <mat-option *ngFor="let data of visitData?.ON" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Nationality</mat-label>
        <mat-select [(ngModel)]="patientDetails.Nationality">
          <mat-option *ngFor="let data of visitData?.NA " [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

    </div>

    <div class="flex space-x-2 w-full pl-2 pr-2">

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Religion</mat-label>
        <mat-select [(ngModel)]="patientDetails.Religion">
          <mat-option *ngFor="let data of visitData?.RE" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Place of Birth</mat-label>
        <mat-select [(ngModel)]="patientDetails.PlaceOfBirth">
          <mat-option *ngFor="let data of visitData?.CY" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Ethnicity</mat-label>
        <mat-select [(ngModel)]="patientDetails.Ethnicity">
          <mat-option *ngFor="let data of visitData?.RC" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Language Spoken</mat-label>
        <mat-select [(ngModel)]="patientDetails.LanguageCode">
          <mat-option *ngFor="let data of visitData?.LG" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="flex space-x-2 w-full pl-2 pr-2">
      <mat-form-field appearance="outline" class="w-1/2">
        <mat-label>Email</mat-label>
        <input matInput type="email" [(ngModel)]="patientDetails.Email" name="email" #email="ngModel" required email
          aria-describedby="emailHelp" maxlength="50" autocomplete="off">
        @if (email.invalid && email.touched && email.errors){
        <mat-error class="text-xs" class="muted small">
          Please enter a valid email address.
        </mat-error>
        }
      </mat-form-field>

      <!-- Personal Phone Numbers -->
      <form [formGroup]="myForm" class="flex gap-x-2 h-max">
        <div class="h-max">
          <ngx-material-intl-tel-input
            [fieldControl]="myForm.get('phoneNumber')"
            [autoIpLookup]="false"
            [appearance]="'outline'"
            [mainLabel]="'Phone 1'">
          </ngx-material-intl-tel-input>
          <div class="error-message text-red-500 text-xs mt-1">
            @if(myForm.get('phoneNumber')?.touched && myForm.get('phoneNumber')?.invalid){
               <span>Please enter a valid number</span>
            }
          </div>
        </div>

        <div class="h-max">
          <ngx-material-intl-tel-input
            [fieldControl]="myForm.get('phoneNumber2')"
            [autoIpLookup]="false"
            [appearance]="'outline'"
            [mainLabel]="'Phone 2'">
          </ngx-material-intl-tel-input>
          <div class="error-message text-red-500 text-xs mt-1">
            @if(myForm.get('phoneNumber2')?.touched && myForm.get('phoneNumber2')?.invalid){
               <span>Please enter a valid number</span>
            }
          </div>
        </div>
      </form>
    </div>
    <div class="flex space-x-2 w-full pl-2 pr-2">
      <section class="example-section">
        <mat-checkbox class="example-margin" [checked]="this.patientDetails.OrganDonor === 'Y'"
          (change)="onCheckboxOrganDonor($event)">Organ Donor</mat-checkbox>

        <mat-checkbox class="example-margin" [checked]="this.patientDetails.SpeakEnglish === 'Y'"
          (change)="onCheckboxSpeakEnglish($event)">
          SpeakEnglish
        </mat-checkbox>

      </section>

      <label for="label" class="text-lg font-bold text-center pt-1.5 ">AlertType :</label>
      <section class="example-section">
        <mat-checkbox class="example-margin" [checked]="this.patientDetails.AlertEmail === 'Y'"
          (change)="onCheckboxAlertEmail($event)">
          AlertEmail
        </mat-checkbox>
        <mat-checkbox class="example-margin" [checked]="this.patientDetails.AlertSMS === 'Y'"
          (change)="onCheckboxAlertSMS($event)">
          AlertSMS
        </mat-checkbox>
      </section>
    </div>
  </mat-tab>

  <mat-tab>
    <ng-template mat-tab-label>
      <mat-icon svgIcon="assignment" class="text-blue-500"></mat-icon>
      <strong class="text-blue-500">Address</strong>
    </ng-template>

    <div class="p-4">
      <!-- Use his-common-address component -->
      <his-common-address
        [addressData]="getAddressData()"
        [visitData]="visitData"
        [phoneForm]="myForm"
        (addressChange)="onAddressChange($event)">
      </his-common-address>

      <div class="flex justify-end mt-4">
        <button
          mat-stroked-button
          color="primary"
          class="bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200 px-6 py-2 rounded-md"
          (click)="addOrEditAddress()">
          {{ selectedAddressIndex !== null ? 'Update' : 'Add' }} Address
        </button>
      </div>
    </div>
    <!-- Address Table with Tailwind CSS -->
    <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Address Type
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Address
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Postal Code
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                State
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                City
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Country
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phone Numbers
              </th>
              <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            @for(data of this.patientDetails.PatAddress; track $index){
              @if(!data?.DBOperationFlag || data?.DBOperationFlag !== 3){
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{data | getAttribute:"DESCRIPTION1"}}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900 max-w-xs">
                    <div class="break-words">
                      {{data.Address1}}
                      @if(data.Address2){, {{data.Address2}}}
                      @if(data.Address3){, {{data.Address3}}}
                      @if(data.Address4){, {{data.Address4}}}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{data.PostalCode}}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{data.State}}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{data.City}}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{data | getAttribute:"DESCRIPTION"}}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    <div class="space-y-1">
                      @if(data.Telephone3){
                        <div class="flex items-center">
                          <span class="text-xs text-gray-500 mr-2">Phone 1:</span>
                          <span>{{data.Telephone3}}</span>
                        </div>
                      }
                      @if(data.Telephone4){
                        <div class="flex items-center">
                          <span class="text-xs text-gray-500 mr-2">Phone 2:</span>
                          <span>{{data.Telephone4}}</span>
                        </div>
                      }
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                    <div class="flex justify-center space-x-2">
                      <button
                        type="button"
                        class="text-blue-600 hover:text-blue-900 transition-colors duration-150"
                        (click)="selectAddressForEdit($index)"
                        title="Edit Address">
                        <mat-icon svgIcon="edit" class="w-5 h-5"></mat-icon>
                      </button>
                      <button
                        type="button"
                        class="text-red-600 hover:text-red-900 transition-colors duration-150"
                        (click)="removeAddress($index)"
                        title="Delete Address">
                        <mat-icon svgIcon="delete_forever" class="w-5 h-5"></mat-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              }
            }
            @if(!patientDetails.PatAddress || patientDetails.PatAddress.length === 0){
              <tr>
                <td colspan="8" class="px-6 py-8 text-center text-sm text-gray-500">
                  No addresses added yet. Click "Add Address" to add the first address.
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  </mat-tab>

  <mat-tab>
    <ng-template mat-tab-label>
      <mat-icon svgIcon="mat_outline:stream" class="text-blue-500"></mat-icon>
      <strong class="text-blue-500">Next of Kin (Emergency Contact)</strong>
    </ng-template>
    <div class="flex space-x-2 w-full pt-3 pl-2 pr-2">
      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>IDNO</mat-label>
        <input matInput placeholder="Search by IdNo" [(ngModel)]="searchQuery" name="IDNo">
        <mat-icon matIconSuffix matTooltip="Search Individual" class="cursor-pointer" svgIcon="person_search"
          (click)="handleIdDialogOpen()">
        </mat-icon>
      </mat-form-field>
      <mat-form-field appearance="outline" class="inputbox">
        <mat-label>Name</mat-label>
        <input matInput placeholder="Name" [(ngModel)]="patientname" readonly>
      </mat-form-field>
      <mat-form-field appearance="outline" class="inputbox">
        <mat-label for="label">Relationship Code</mat-label>
        <mat-select [(ngModel)]="nok.RelationshipCode" required>
          <mat-option *ngFor="let data of visitData?.RT" [value]="data.IDENTIFIER">{{data.DESCRIPTION}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </mat-tab>

  @if(page==="manualVisit"){
  <mat-tab>
    <ng-template mat-tab-label>
      <strong class="text-blue-500">Payer Details</strong>
    </ng-template>
    <div class="pt-1">
      <his-pat-payerlist [payerDetails]="_payerDetails" (handleArDialog)="handleArModal($event)"></his-pat-payerlist>
    </div>
  </mat-tab>
  }
</mat-tab-group>

import { MatSnackBar } from '@angular/material/snack-bar';
import { AfterViewInit, Component, ElementRef, EventEmitter, inject, Input, Output, ViewChild } from '@angular/core';
import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormField, MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { EcmedWebapiModelsPatientmgmtEditModel,
         EcmedWebapiModelsPatientmgmtNextOfKinModel,
         EcmedWebapiModelsPatientmgmtPatientAddressModel } from 'ecmed-api/visitmgmt';
import { VisitData } from '../visit.model';
import { MatDialog } from '@angular/material/dialog';
import { IndividualDetailDialogComponent } from '../individual-detail-dialog/individual-detail-dialog.component';
import { AutoFormatDateDirective } from '@his-components/services';
import { NgxMaterialIntlTelInputComponent } from 'ngx-material-intl-tel-input';
import { PatientPayerListComponent } from '../payertable/payertable.component';
import { ApplicationService, GetAttributePipe } from 'ec-ngcore';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTabsModule } from '@angular/material/tabs';
import { MaterialPackage } from '@his-components/utils';
import { HisCommonAddressComponent } from '../../../../../app-appt/src/components/his-common-address/his-common-address.component';
@Component({
  selector: 'his-pat-form',
  standalone: true,
  templateUrl: './patient-form.component.html',
  styleUrl: './patient-form.component.scss',
  providers: [DatePipe, DecimalPipe,
  ],
  imports: [PatientPayerListComponent, NgxMaterialIntlTelInputComponent, CommonModule, MaterialPackage, FormsModule,
    AutoFormatDateDirective, ReactiveFormsModule, MatFormFieldModule, MatInputModule, GetAttributePipe, HisCommonAddressComponent]
})

export class PatientFormComponent implements AfterViewInit {
  private _appService: ApplicationService=inject(ApplicationService);
  constructor(
    public snackbar: MatSnackBar,
    private fb: FormBuilder,
    public dialog: MatDialog,
    ) {
    this.myForm = this.fb.group({
      phoneNumber: [''],
      phoneNumber2: [''],
      phoneNumber3: [''],
      phoneNumber4: ['']
    });
  }

  @ViewChild('inputRef') inputElement?: ElementRef;
  patientDetails: EcmedWebapiModelsPatientmgmtEditModel = {};
  selectedPatient: EcmedWebapiModelsPatientmgmtEditModel = {};
  patientID: any;
  today = new Date();
  isClicked: boolean = false;
  patientDetail: any;
  address: EcmedWebapiModelsPatientmgmtPatientAddressModel = {};
  nok: EcmedWebapiModelsPatientmgmtNextOfKinModel = {};
  isButtonVisible: boolean = false;
  searchQuery?: string;
  identifier?: string;
  patientname?: string;
  searchname: any;
  searchtype: any;
  selectedAddressIndex: number | null = null;
  _patient: any;
  _payerDetails: any = []
  myForm: FormGroup;

  @Input() isLoading: boolean = true;
  @Input() page: any;
  @Input() visitData?: VisitData;

  @Input() set patient(value: any) {
    if (value) {
      this._patient = value
      this.fetchDetail();
      this.handleAllocatePhone(value)
      this.handleGetPayerDetails.emit()
    }
  }
  @Input() set savedindividualresult(value: any) {
    if (value) {
      this.nok.IndividualId = value['IDENTIFIER']
      this.patientname = value['NAME']
      this.searchQuery = value['IDNO'];
      this.searchtype = value['IDTYPE'];
    }
  }
 
  @Input() set payerDetails(value:any[]) {
    if (value?.length) {
      this._payerDetails = value
    } else {
      this._payerDetails = []
    }
  }

  @Output() dateChanged: EventEmitter<string> = new EventEmitter<string>();
  @Output() saveEvent = new EventEmitter<any>();
  @Output() savePatientRequest: EventEmitter<void> = new EventEmitter<void>()
  @Output() searchDataEvent = new EventEmitter<any>();
  @Output() handleIndividualDialog = new EventEmitter<any>()
  @Output() saveedit = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<void>();
  @Output() handleEnterPress = new EventEmitter<any>()
  @Output() save: EventEmitter<void> = new EventEmitter<void>();
  @Output() handleGetPayerDetails = new EventEmitter<any>()
  @Output() handleArDialog = new EventEmitter<any>()

  ngAfterViewInit() {
    setTimeout(() => this.inputElement?.nativeElement.focus(), 100); // Focus after 100ms
  }

  phoneValidator(control: AbstractControl) {
    const valid = /(\+\d{1,3}[- ]?)?\d{10}/.test(control.value); // Example regex for phone number validation
    return valid ? null : { invalidPhone: true };
  }
  public handleAllocatePhone(value:any) {
    if (value?.PatientAddress[0]) {
      this.myForm = new FormGroup({
        phoneNumber: new FormControl(value?.PatientAddress[0]?.TEL || ''),
        phoneNumber2: new FormControl(value?.PatientAddress[0]?.TEL2 || ''),
        phoneNumber3: new FormControl(''),
        phoneNumber4: new FormControl(''),
      });

    }
  }

  public handleValidate = () => {
    let formObj:any = {
      IDNO: this.patientDetails.IDNo,
      IDType: this.patientDetails.IDType?.toString(),
      title: this.patientDetails.TitleCode,
      name: this.patientDetails.Name,
      gender: this.patientDetails.Gender,
      dob: this.patientDetails.DateOfBirth,
      email: this.patientDetails.Email,
      maritalStatus: this.patientDetails.MaritalStatus,
      //telephone: this.patientDetails.Telephone,
      address: this.patientDetails.PatAddress,
      kinName: this.patientname,
      relationshipCode: this.nok.RelationshipCode
    }, formKey = Object.keys(formObj), text = ""
    
    for (let i = 0; i < formKey.length; i++) {
      if (!formObj[formKey[i]]) {
        if (formKey[i] === "kinName") {
          text = "NOK is required"
        } else if (formKey[i] === "telephone") {
          text = "Contact info required."
        } else if (formKey[i] === "address") {
          text = "Address info required."
        } else {
          text = (formKey[i].toUpperCase() + " " + "is empty.")
        }
        break
      }
    }
    return (text)
  }

  public resetScreen(): void {
    if (this.patientID) {
      this.patientDetails = { ...this.selectedPatient };
      this.patientname = "";

    } else {
      this.patientDetails = {};
    }
  }

  public handleFindPatient(ele:KeyboardEvent) {
    if (ele?.keyCode === 13) {
      this.handleEnterPress.emit({ IdNo: this.patientDetails.IDNo, IdType: this.patientDetails.IDType })
    }
  }

  public handleSaveUpdatePatient(type:string) {
    this.patientDetails.Telephone = this.myForm?.value?.phoneNumber
    this.patientDetails.Telephone2 = this.myForm?.value?.phoneNumber2

    let result = this.handleValidate()
    if (result) {
      this.snackbar.open(result, "", {
        horizontalPosition: 'right',
        verticalPosition: 'top',
        duration: 2000,
        panelClass: ['custom-snackbar', 'snackbar-success']
      })
      this._appService.alertDialog({
        'title': 'System Error',
        message: result
      });
      return;
    }

    if (!this.patientDetails)
    {return;}
    let Addressadd:any[] = [];
    this.patientDetails.PatAddress?.forEach((obj)=>{
      let element = obj;
      let flag: any = 1;
      if (element?.Identifier && parseInt(element?.Identifier)) {
        flag = (element?.DBOperationFlag === 3) ? 3 : (element?.DBOperationFlag === 2 || element?.DBOperationFlag === 1) ? 2 : 2
      }

      Addressadd.push({
        Identifier: (element?.Identifier || 0).toString(),
        PatientId: "",
        AddressType: element.AddressType,
        IsDefault: "1",
        Active: "1",
        Telephone: element.Telephone,
        Telephone2: element.Telephone2,
        Telephone3: element.Telephone3,
        Telephone4: element.Telephone4,
        Address1: element.Address1,
        Address2: element.Address2,
        Address3: element.Address3,
        Address4: element.Address4,
        City: element.City?.toString(),
        State: element.State?.toString(),
        PostalCode: element.PostalCode,
        Country: element.Country,
        DBOperationFlag: flag
      });
    });

    let objSave = {}
    if (type === "save") {
      objSave = {
        Identifier: "",
        _OrgCode: 0,
        Merged: "N",
        ...this.patientDetails,
        Telephone: this.patientDetails.Telephone ,
        Telephone2: this.patientDetails.Telephone2,

        NOK: [{
          PatientId: '',
          RelationshipCode: this.nok.RelationshipCode,
          IndividualId: this.nok.IndividualId?.toString(),
        }],
        PatAddress: Addressadd,

      };
    } else if (type === "update") {
      objSave = {
        Identifier: this._patient.Detail[0].IDENTIFIER.toString(),
        _OrgCode: this._patient.Detail[0]._ORGCODE,
        Merged: "N",
        ...this.patientDetails,
        Telephone: this.patientDetails.Telephone || '',
        Telephone2: this.patientDetails.Telephone2 || '',
        PatAddress: Addressadd,
        NOK: [{
          PatientId: this._patient.Detail[0].IDENTIFIER.toString(),
          RelationshipCode: this.nok.RelationshipCode,
          IndividualId: this.nok.IndividualId?.toString(),
        }],
      };
    }
    let requestPatientData: any = JSON.stringify(objSave, null, 2);
    if (type === "save") {
      this.saveEvent.emit(requestPatientData);
    } else if (type === "update") {
      this.saveedit.emit(requestPatientData);
    }
  }

  fetchDetail() {
    if (this._patient?.Detail) {
      this.patientDetails.HRN = this._patient.Detail[0].HRN;
      this.patientDetails.Name = this._patient.Detail[0].NAME;
      this.patientDetails.Email = this._patient.Detail[0].EMAIL;
      this.patientDetails.IDType = this._patient.Detail[0].IDTYPE;
      this.patientDetails.AlertEmail = this._patient.Detail[0].ALERTEMAIL;
      this.patientDetails.AlertSMS = this._patient.Detail[0].ALERTSMS;
      this.patientDetails.BloodGroup = this._patient.Detail[0].BLOODGROUP;
      this.patientDetails.DateOfBirth = this._patient.Detail[0].DOB;
      this.patientDetails.Education = this._patient.Detail[0].EDUCATION;
      this.patientDetails.Ethnicity = this._patient.Detail[0].RACE;
      this.patientDetails.Gender = this._patient.Detail[0].SEX;
      this.patientDetails.IDNo = this._patient.Detail[0].IDNO;
      this.patientDetails.LanguageCode = this._patient.Detail[0].LANGUAGECODE;
      this.patientDetails.Nationality = this._patient.Detail[0].NATIONALITY;
      this.patientDetails.Occupation = this._patient.Detail[0].OCCUPATION;
      this.patientDetails.MaritalStatus = this._patient.Detail[0].MARITALSTATUS;
      this.patientDetails.OrganDonor = this._patient.Detail[0].ORGANDONOR;
      this.patientDetails.PlaceOfBirth = this._patient.Detail[0].PLACE_OF_BIRTH;
      this.patientDetails.Religion = this._patient.Detail[0].RELIGION;
      this.patientDetails.ResidenceCode = this._patient.Detail[0].ResidenceCode;
      this.patientDetails.VIPStatus = this._patient.Detail[0].VIPStatus;
      this.patientDetails.PatientType = this._patient.Detail[0].PATIENTTYPE?.toString();
      this.patientDetails.TitleCode = this._patient.Detail[0].TITLECODE;
      this.patientDetails.SpeakEnglish = this._patient.Detail[0].SPEAKENGLISH;
      this.patientDetails.Telephone = this._patient.Detail[0].TEL;
      this.patientDetails.Telephone2 = this._patient.Detail[0].TEL2;
      this.patientDetails.Telephone3 = this._patient.Detail[0].TEL3;
      this.patientDetails.Telephone4 = this._patient.Detail[0].TEL4;
      this.nok.RelationshipCode = this._patient.NextOfKin[0].RELATIONSHIPCODE,
        this.nok.IndividualId = this._patient.NextOfKin[0].INDIVIDUALID,
        this.searchQuery = this._patient.NextOfKin[0].IDNO,
        this.patientname = this._patient.NextOfKin[0].NAME,
        this.patientDetails.PatAddress = (this._patient.PatientAddress || []).map((element:any) => {
          return {
            Identifier: element.IDENTIFIER,
            PatientId: element.PATIENTID,
            AddressType: element.ADDRESSTYPE,
            IsDefault: element.ISDEFAULT,
            Active: element.Active,
            Telephone: element.TEL,
            Telephone2: element.TEL2,
            Telephone3: element.TEL3,
            Telephone4: element.TEL4,
            Address1: element.ADDRESS1,
            Address2: element.ADDRESS2,
            Address3: element.ADDRESS3,
            Address4: element.ADDRESS4,
            City: element.CITY,
            State: element.STATE,
            PostalCode: element.POSTALCODE,
            Country: element.COUNTRY,
            DESCRIPTION: element.COUNTRYDESC,
            DESCRIPTION1: element.ADDRESSTYPE_DESC,
          };
        });
    }
  }

  public backToPatientList() {
    this.cancel.emit();
  }

  onDateChanged(event: any): void {
    this.dateChanged.emit(this.patientDetails.DateOfBirth || '' );
  }

  calculateAge(): string {
    if (!this.patientDetails.DateOfBirth) {
      return '';
    }
    const dob: Date = new Date(this.patientDetails.DateOfBirth);
    const today: Date = new Date();
    const diffInMilliseconds: number = Math.abs(today.getTime() - dob.getTime());
    const age: number = Math.floor(diffInMilliseconds / (1000 * 3600 * 24 * 365.25));
    return age.toString();
  }

  onCheckboxOrganDonor(event: any) {
    this.patientDetails.OrganDonor = event.checked ? 'Y' : 'N';
  }

  onCheckboxSpeakEnglish(event: any) {
    this.patientDetails.SpeakEnglish = event.checked ? 'Y' : 'N';
  }

  onCheckboxAlertEmail(event: any) {
    this.patientDetails.AlertEmail = event.checked ? 'Y' : 'N';
  }

  onCheckboxAlertSMS(event: any) {
    this.patientDetails.AlertSMS = event.checked ? 'Y' : 'N';
  }

  restrictToNumbersAndPlus(event: KeyboardEvent) {
    const allowedKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', 'Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete', 'Enter'];
    if (!allowedKeys.includes(event.key)) {
      event.preventDefault();
    }
    if (event.key === '+' && (<HTMLInputElement>event.currentTarget).selectionStart !== 0) {
      event.preventDefault();
    }
  }

  public addOrEditAddress(): void {
    if (this.selectedAddressIndex !== null) {
      this.editAddress();
    } else {
      this.addAddress();
    }
  }

  public addAddress(): void {
    // Validate required fields
    if (!this.address.AddressType || !this.address.Address1) {
      this.snackbar.open('Please fill in Address Type and Address1 fields', '', {
        horizontalPosition: 'right',
        verticalPosition: 'top',
        duration: 3000,
        panelClass: ['custom-snackbar', 'snackbar-error']
      });
      return;
    }

    let element = {
      AddressType: this.address.AddressType,
      Address1: this.address.Address1,
      Address2: this.address.Address2,
      Address3: this.address.Address3,
      Address4: this.address.Address4,
      Telephone: this.myForm?.value?.phoneNumber,
      Telephone2: this.myForm?.value?.phoneNumber2,
      Telephone3: this.myForm?.value?.phoneNumber3,
      Telephone4: this.myForm?.value?.phoneNumber4,
      City: this.address.City,
      State: this.address.State,
      Country: this.address.Country,
      PostalCode: this.address.PostalCode,
      DBOperationFlag: 1,
      DESCRIPTION: this.visitData?.CY.find((d: { IDENTIFIER: any; }) => d.IDENTIFIER === this.address.Country)?.DESCRIPTION,
      DESCRIPTION1: this.visitData?.AD.find((d: { IDENTIFIER: any; }) => d.IDENTIFIER === this.address.AddressType)?.DESCRIPTION,
      Identifier: "",
      PatientId: "",
    };

    this.patientDetails.PatAddress = this.patientDetails.PatAddress || [];
    this.patientDetails.PatAddress.push({
      ...element,
    });

    // Clear form after successful add
    this.clearAddressForm();

    // Show success message
    this.snackbar.open('Address added successfully', '', {
      horizontalPosition: 'right',
      verticalPosition: 'top',
      duration: 2000,
      panelClass: ['custom-snackbar', 'snackbar-success']
    });
  }

  public removeAddress(index: number) {
    if (this.patientDetails && this.patientDetails.PatAddress &&
        this.patientDetails.PatAddress.length < index)
        this.patientDetails.PatAddress[index].DBOperationFlag = 3
    this.address = {};
  }

  public editAddress(): void {
    if (this.selectedAddressIndex === null) {
      return;
    }

    // Validate required fields
    if (!this.address.AddressType || !this.address.Address1) {
      this.snackbar.open('Please fill in Address Type and Address1 fields', '', {
        horizontalPosition: 'right',
        verticalPosition: 'top',
        duration: 3000,
        panelClass: ['custom-snackbar', 'snackbar-error']
      });
      return;
    }

    const editedAddress = {
      ...this.patientDetails.PatAddress![this.selectedAddressIndex],
      AddressType: this.address.AddressType,
      Address1: this.address.Address1,
      Address2: this.address.Address2,
      Address3: this.address.Address3,
      Address4: this.address.Address4,
      Telephone: this.myForm?.value?.phoneNumber,
      Telephone2: this.myForm?.value?.phoneNumber2,
      Telephone3: this.myForm?.value?.phoneNumber3,
      Telephone4: this.myForm?.value?.phoneNumber4,
      City: this.address.City,
      State: this.address.State,
      Country: this.address.Country,
      PostalCode: this.address.PostalCode,
      DBOperationFlag: 2,
      DESCRIPTION: this.visitData?.CY.find((d: { IDENTIFIER: any }) => d.IDENTIFIER === this.address.Country)?.DESCRIPTION,
      DESCRIPTION1: this.visitData?.AD.find((d: { IDENTIFIER: any }) => d.IDENTIFIER === this.address.AddressType)?.DESCRIPTION,
    };

    if (this.patientDetails && this.patientDetails.PatAddress) {
      this.patientDetails.PatAddress[this.selectedAddressIndex] = editedAddress;
    }

    // Clear form after successful edit
    this.clearAddressForm();

    // Show success message
    this.snackbar.open('Address updated successfully', '', {
      horizontalPosition: 'right',
      verticalPosition: 'top',
      duration: 2000,
      panelClass: ['custom-snackbar', 'snackbar-success']
    });
  }

  public selectAddressForEdit(index: number): void {
    this.selectedAddressIndex = index;
    if (!(this.patientDetails && this.patientDetails.PatAddress))
       return;

    const selectedAddress = this.patientDetails.PatAddress[index];

    // Populate address object for his-common-address component
    this.address = {
      AddressType: selectedAddress.AddressType,
      Address1: selectedAddress.Address1,
      Address2: selectedAddress.Address2,
      Address3: selectedAddress.Address3,
      Address4: selectedAddress.Address4,
      Country: selectedAddress.Country,
      State: selectedAddress.State,
      City: selectedAddress.City,
      PostalCode: selectedAddress.PostalCode
    };

    // Update form with phone numbers
    this.myForm.patchValue({
      phoneNumber: selectedAddress.Telephone || '',
      phoneNumber2: selectedAddress.Telephone2 || '',
      phoneNumber3: selectedAddress.Telephone3 || '',
      phoneNumber4: selectedAddress.Telephone4 || ''
    });
  }

  public handleIdDialogOpen() {
    let dialogRef = this.dialog.open(IndividualDetailDialogComponent, {
      width: "900vw", height: "90vh"
    })

    dialogRef.afterClosed().subscribe(res => {
      if (typeof res === "object") {
        this.searchQuery = res?.data?.element["IDNO"]
        this.searchtype = res?.data?.element["IDTYPE"]
        this.patientname = res?.data?.element["NAME"]
        this.nok.IndividualId = res?.data?.element["IDENTIFIER"]
      }
    })
  }

  public handleIndividualDialogOpen() {
    this.handleIndividualDialog.emit()
  }

  public handlePayerDetails() {
    this.handleGetPayerDetails.emit()
  }

  public handleArModal(element:any) {
    this.handleArDialog.emit(element)
  }

  // Methods for his-common-address component integration
  public getAddressData(): any {
    return {
      ADDRESSTYPE: this.address.AddressType,
      ADDRESS1: this.address.Address1,
      ADDRESS2: this.address.Address2,
      ADDRESS3: this.address.Address3,
      ADDRESS4: this.address.Address4,
      COUNTRY: this.address.Country,
      STATE: this.address.State,
      CITY: this.address.City,
      POSTALCODE: this.address.PostalCode
    };
  }

  public onAddressChange(addressData: any): void {
    this.address.AddressType = addressData.ADDRESSTYPE;
    this.address.Address1 = addressData.ADDRESS1;
    this.address.Address2 = addressData.ADDRESS2;
    this.address.Address3 = addressData.ADDRESS3;
    this.address.Address4 = addressData.ADDRESS4;
    this.address.Country = addressData.COUNTRY;
    this.address.State = addressData.STATE;
    this.address.City = addressData.CITY;
    this.address.PostalCode = addressData.POSTALCODE;
  }

  public clearAddressForm(): void {
    this.selectedAddressIndex = null;
    this.address = {};
    // Reset phone form controls for address
    this.myForm.patchValue({
      phoneNumber3: '',
      phoneNumber4: ''
    });
  }

  allCountries = [
    "AF", "AX", "AL", "DZ", "AS", "AD", "AO", "AI", "AQ", "AG", "AR", "AM", "AW", "AC", "AU", "AT", "AZ", "BS", "BH",
    "BD", "BB", "BY", "BE", "BZ", "BJ", "BM", "BT", "BO", "BA", "BW", "BR", "IO", "BN", "BG", "BF", "BI", "KH", "CM",
    "CA", "CV", "KY", "CF", "TD", "CL", "CN", "CX", "CC", "CO", "KM", "CG", "CK", "CR", "CI", "HR", "CU", "CY", "CZ",
    "CD", "DK", "DJ", "DM", "DO", "EC", "EG", "SV", "GQ", "ER", "EE", "SZ", "ET", "FK", "FO", "FJ", "FI", "FR", "GF",
    "PF", "GA", "GM", "GE", "DE", "GH", "GI", "GR", "GL", "GD", "GP", "GU", "GT", "GG", "GN", "GW", "GY", "HT", "VA",
    "HN", "HK", "HU", "IS", "IN", "ID", "IR", "IQ", "IE", "IM", "IL", "IT", "JM", "JP", "JE", "JO", "KZ", "KE", "KI",
    "KP", "KR", "XK", "KW", "KG", "LA", "LV", "LB", "LS", "LR", "LY", "LI", "LT", "LU", "MO", "MG", "MW", "MY", "MV",
    "ML", "MT", "MH", "MQ", "MR", "MU", "YT", "MX", "FM", "MD", "MC", "MN", "ME", "MS", "MA", "MZ", "MM", "NA", "NR",
    "NP", "NL", "NC", "NZ", "NI", "NE", "NG", "NU", "NF", "MK", "MP", "NO", "OM", "PK", "PW", "PS", "PA", "PG", "PY",
    "PE", "PH", "PN", "PL", "PT", "PR", "QA", "RE", "RO", "RU", "RW", "BL", "SH", "KN", "LC", "MF", "PM", "VC", "WS",
    "SM", "ST", "SA", "SN", "RS", "SC", "SL", "SG", "SX", "SK", "SI", "SB", "SO", "ZA", "GS", "SS", "ES", "LK", "SD",
    "SR", "SJ", "SE", "CH", "SY", "TW", "TJ", "TZ", "TH", "TL", "TG", "TK", "TO", "TT", "TN", "TR", "TM", "TC", "TV",
    "UG", "UA", "AE", "GB", "US", "UY", "UZ", "VU", "VE", "VN", "VG", "VI", "WF", "YE", "ZM", "ZW"
  ];

}
